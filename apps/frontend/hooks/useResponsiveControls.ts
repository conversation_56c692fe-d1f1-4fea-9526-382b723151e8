/**
 * 响应式控制面板Hook
 * 🎯 核心价值：智能的响应式控制面板显示逻辑
 * 📦 功能范围：窗口尺寸监听、自动显示/隐藏控制面板
 * 🔄 架构设计：基于窗口尺寸的自动化UI控制
 */

'use client';

import { useCallback, useEffect, useRef, useState } from 'react';

// ===== 配置常量 =====

/** 响应式配置 */
const RESPONSIVE_CONFIG = {
  /** 矩阵最大尺寸 */
  MATRIX_MAX_SIZE: 1122, // 33 * 34px
  /** 控制面板宽度 */
  CONTROLS_WIDTH: 320, // w-80 = 320px
  /** 最小边距 */
  MIN_MARGIN: 32, // 左右各16px边距
  /** 自动隐藏临界点 */
  get AUTO_HIDE_BREAKPOINT() {
    return this.MATRIX_MAX_SIZE + this.CONTROLS_WIDTH + this.MIN_MARGIN;
  }
} as const;

// ===== Hook接口 =====

interface UseResponsiveControlsReturn {
  /** 当前窗口宽度 */
  windowWidth: number;
  /** 当前窗口高度 */
  windowHeight: number;
  /** 是否应该悬浮显示控制面板 */
  shouldFloat: boolean;
  /** 是否为移动设备 */
  isMobile: boolean;
  /** 是否为平板设备 */
  isTablet: boolean;
  /** 是否为桌面设备 */
  isDesktop: boolean;
  /** 控制面板是否可见 */
  controlsVisible: boolean;
  /** 设置控制面板可见性 */
  setControlsVisible: (visible: boolean) => void;
  /** 切换控制面板可见性 */
  toggleControls: () => void;
  /** 控制面板显示模式 */
  displayMode: 'normal' | 'floating' | 'hidden';
}

// ===== 主Hook =====

export const useResponsiveControls = (): UseResponsiveControlsReturn => {
  // 状态管理
  const [windowWidth, setWindowWidth] = useState(0);
  const [windowHeight, setWindowHeight] = useState(0);
  const [controlsVisible, setControlsVisible] = useState(true);
  const [isClient, setIsClient] = useState(false);
  const [userControlled, setUserControlled] = useState(false); // 跟踪用户是否手动控制

  // 用户操作锁定机制 - 防止自动逻辑干扰用户操作
  const [userActionLock, setUserActionLock] = useState(false);
  const userActionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 确保客户端渲染
  useEffect(() => {
    setIsClient(true);
  }, []);

  // 窗口尺寸监听
  useEffect(() => {
    if (!isClient) return;

    const handleResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      setWindowWidth(width);
      setWindowHeight(height);
    };

    // 初始化
    handleResize();

    // 添加监听器
    window.addEventListener('resize', handleResize);

    // 清理
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [isClient]);

  // 统一的用户操作处理函数
  const performUserAction = useCallback((action: () => void, actionName: string) => {
    console.log(`🎯 执行用户操作: ${actionName}`, {
      windowWidth,
      currentVisible: controlsVisible,
      userControlled,
      userActionLock
    });

    // 清除之前的锁定定时器
    if (userActionTimeoutRef.current) {
      clearTimeout(userActionTimeoutRef.current);
    }

    // 设置用户操作锁定
    setUserActionLock(true);
    setUserControlled(true);

    // 执行用户操作
    action();

    // 500ms后解除锁定，防止自动逻辑干扰
    userActionTimeoutRef.current = setTimeout(() => {
      setUserActionLock(false);
      console.log(`🔓 用户操作锁定解除: ${actionName}`);
    }, 500);
  }, [windowWidth, controlsVisible, userControlled, userActionLock]);

  // 响应式控制面板可见性管理
  useEffect(() => {
    if (windowWidth === 0) return; // 避免初始化时的误触发
    if (userActionLock) {
      console.log('🔒 用户操作锁定中，跳过自动响应式调整');
      return; // 用户操作锁定期间，跳过自动调整
    }

    const isSmallWindow = windowWidth < RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT;

    console.log('📱 响应式逻辑检查', {
      windowWidth,
      isSmallWindow,
      controlsVisible,
      userControlled,
      userActionLock,
      breakpoint: RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT
    });

    // 当窗口变小时，只有在用户没有手动控制的情况下才自动隐藏控制面板
    if (isSmallWindow && controlsVisible && !userControlled) {
      console.log('📱 窗口变小，自动隐藏控制面板');
      setControlsVisible(false);
    }
    // 当窗口变大时，自动显示控制面板并重置用户控制状态
    else if (!isSmallWindow && !controlsVisible) {
      console.log('🖥️ 窗口变大，自动显示控制面板');
      setControlsVisible(true);
      setUserControlled(false); // 重置用户控制状态
    }
  }, [windowWidth, controlsVisible, userControlled, userActionLock]);

  // 计算响应式状态
  const shouldFloat = windowWidth > 0 && windowWidth < RESPONSIVE_CONFIG.AUTO_HIDE_BREAKPOINT;
  const isMobile = windowWidth > 0 && windowWidth < 768;
  const isTablet = windowWidth >= 768 && windowWidth < 1024;
  const isDesktop = windowWidth >= 1024;

  // 计算显示模式
  const displayMode: 'normal' | 'floating' | 'hidden' =
    shouldFloat ? (controlsVisible ? 'floating' : 'hidden') :
      (controlsVisible ? 'normal' : 'hidden');

  // 切换控制面板可见性
  const toggleControls = useCallback(() => {
    performUserAction(() => {
      setControlsVisible(prev => !prev);
    }, 'toggleControls');
  }, [performUserAction]);

  // 设置控制面板可见性（包装原始setter以标记用户控制）
  const setControlsVisibleWithUserControl = useCallback((visible: boolean) => {
    performUserAction(() => {
      setControlsVisible(visible);
    }, `setControlsVisible(${visible})`);
  }, [performUserAction]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (userActionTimeoutRef.current) {
        clearTimeout(userActionTimeoutRef.current);
      }
    };
  }, []);

  return {
    windowWidth,
    windowHeight,
    shouldFloat,
    isMobile,
    isTablet,
    isDesktop,
    controlsVisible,
    setControlsVisible: setControlsVisibleWithUserControl,
    toggleControls,
    displayMode,
  };
};

// ===== 导出配置 =====

export { RESPONSIVE_CONFIG };

// ===== 类型导出 =====

export type { UseResponsiveControlsReturn };

