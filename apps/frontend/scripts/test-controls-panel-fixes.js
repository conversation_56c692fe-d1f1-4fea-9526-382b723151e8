/**
 * 控制面板修复验证测试脚本
 * 🎯 核心价值：验证隐藏按钮修复和点击外部自动隐藏功能
 * 📦 功能范围：按钮点击测试、外部点击检测、事件处理验证
 * 🔄 架构设计：自动化测试脚本，确保控制面板功能正常工作
 */

// 测试配置
const TEST_CONFIG = {
  CLICK_DELAY: 500, // 点击间隔时间
  ANIMATION_DELAY: 300, // 动画等待时间
};

// 测试控制面板修复功能
function testControlsPanelFixes() {
  console.log('🔍 开始测试控制面板修复功能...');
  
  const results = {
    hideButtonTest: false,
    clickOutsideTest: false,
    eventHandlingTest: false,
    responsiveTest: false,
    issues: [],
    recommendations: []
  };
  
  // 测试1: 隐藏按钮功能
  console.log('🔘 测试隐藏按钮功能...');
  testHideButton(results);
  
  // 测试2: 点击外部自动隐藏
  console.log('👆 测试点击外部自动隐藏...');
  testClickOutside(results);
  
  // 测试3: 事件处理
  console.log('⚡ 测试事件处理...');
  testEventHandling(results);
  
  // 测试4: 响应式行为
  console.log('📱 测试响应式行为...');
  testResponsiveBehavior(results);
  
  // 输出测试结果
  outputTestResults(results);
  
  return results;
}

// 测试隐藏按钮功能
function testHideButton(results) {
  const hideButtons = document.querySelectorAll('[title="隐藏控制面板"]');
  
  if (hideButtons.length === 0) {
    console.log('⚠️ 未找到隐藏按钮');
    results.issues.push('隐藏按钮不存在');
    return;
  }
  
  hideButtons.forEach((button, index) => {
    console.log(`检查隐藏按钮 ${index + 1}:`, button);
    
    // 检查按钮是否可点击
    const isClickable = !button.disabled && 
                       getComputedStyle(button).pointerEvents !== 'none';
    
    if (!isClickable) {
      console.log(`❌ 隐藏按钮 ${index + 1} 不可点击`);
      results.issues.push(`隐藏按钮 ${index + 1} 不可点击`);
      return;
    }
    
    // 检查按钮事件监听器
    const hasClickHandler = button.onclick !== null || 
                           button.getAttribute('onclick') !== null;
    
    if (hasClickHandler) {
      console.log(`✅ 隐藏按钮 ${index + 1} 有点击事件处理器`);
      results.hideButtonTest = true;
    } else {
      console.log(`❌ 隐藏按钮 ${index + 1} 缺少点击事件处理器`);
      results.issues.push(`隐藏按钮 ${index + 1} 缺少点击事件处理器`);
    }
  });
}

// 测试点击外部自动隐藏
function testClickOutside(results) {
  const floatingPanel = document.querySelector('.fixed.top-4.right-4');
  
  if (!floatingPanel) {
    console.log('ℹ️ 当前没有悬浮控制面板，跳过点击外部测试');
    results.clickOutsideTest = true; // 不是错误，只是当前状态
    return;
  }
  
  console.log('找到悬浮控制面板:', floatingPanel);
  
  // 模拟点击外部区域
  const outsideElement = document.querySelector('.matrix-area');
  if (outsideElement) {
    console.log('✅ 找到外部点击区域');
    
    // 检查是否有document点击监听器
    const hasDocumentListener = typeof window.testClickOutside === 'function';
    
    if (hasDocumentListener) {
      console.log('✅ 检测到document点击监听器');
      results.clickOutsideTest = true;
    } else {
      console.log('⚠️ 未检测到document点击监听器');
      results.recommendations.push('建议添加document点击监听器用于外部点击检测');
    }
  } else {
    console.log('❌ 未找到外部点击区域');
    results.issues.push('缺少外部点击区域');
  }
}

// 测试事件处理
function testEventHandling(results) {
  // 检查事件冒泡处理
  const buttons = document.querySelectorAll('button[title*="控制面板"]');
  
  buttons.forEach((button, index) => {
    // 检查按钮是否有正确的事件处理
    const rect = button.getBoundingClientRect();
    const isVisible = rect.width > 0 && rect.height > 0;
    
    if (isVisible) {
      console.log(`✅ 按钮 ${index + 1} 可见且可交互`);
      results.eventHandlingTest = true;
    } else {
      console.log(`❌ 按钮 ${index + 1} 不可见或不可交互`);
      results.issues.push(`按钮 ${index + 1} 不可见或不可交互`);
    }
  });
}

// 测试响应式行为
function testResponsiveBehavior(results) {
  const currentWidth = window.innerWidth;
  const controlsPanel = document.querySelector('.controls-sidebar');
  const floatingPanel = document.querySelector('.fixed.top-4.right-4');
  const menuButton = document.querySelector('[title="显示控制面板"]');
  
  console.log(`当前窗口宽度: ${currentWidth}px`);
  
  // 检查当前显示模式
  if (controlsPanel) {
    console.log('✅ 检测到侧边栏模式');
    results.responsiveTest = true;
  } else if (floatingPanel) {
    console.log('✅ 检测到悬浮模式');
    results.responsiveTest = true;
  } else if (menuButton) {
    console.log('✅ 检测到隐藏模式');
    results.responsiveTest = true;
  } else {
    console.log('❌ 未检测到任何控制面板模式');
    results.issues.push('控制面板模式检测失败');
  }
}

// 输出测试结果
function outputTestResults(results) {
  console.log('\n📊 测试结果汇总:');
  console.log('==================');
  
  const tests = [
    { name: '隐藏按钮功能', result: results.hideButtonTest },
    { name: '点击外部自动隐藏', result: results.clickOutsideTest },
    { name: '事件处理', result: results.eventHandlingTest },
    { name: '响应式行为', result: results.responsiveTest }
  ];
  
  tests.forEach(test => {
    const status = test.result ? '✅ 通过' : '❌ 失败';
    console.log(`${test.name}: ${status}`);
  });
  
  const passedTests = tests.filter(test => test.result).length;
  const totalTests = tests.length;
  
  console.log(`\n总体结果: ${passedTests}/${totalTests} 测试通过`);
  
  if (results.issues.length > 0) {
    console.log('\n🚨 发现的问题:');
    results.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  }
  
  if (results.recommendations.length > 0) {
    console.log('\n💡 改进建议:');
    results.recommendations.forEach((rec, index) => {
      console.log(`${index + 1}. ${rec}`);
    });
  }
  
  if (passedTests === totalTests && results.issues.length === 0) {
    console.log('\n🎉 所有测试通过！控制面板功能正常工作。');
  }
}

// 手动测试函数
function manualTestHideButton() {
  console.log('🔘 手动测试隐藏按钮...');
  
  const hideButton = document.querySelector('[title="隐藏控制面板"]');
  if (hideButton) {
    console.log('找到隐藏按钮，模拟点击...');
    hideButton.click();
    
    setTimeout(() => {
      const panelStillVisible = document.querySelector('.controls-sidebar') || 
                               document.querySelector('.fixed.top-4.right-4');
      
      if (!panelStillVisible) {
        console.log('✅ 隐藏按钮工作正常');
      } else {
        console.log('❌ 隐藏按钮未能隐藏控制面板');
      }
    }, TEST_CONFIG.ANIMATION_DELAY);
  } else {
    console.log('❌ 未找到隐藏按钮');
  }
}

function manualTestClickOutside() {
  console.log('👆 手动测试点击外部...');
  
  const floatingPanel = document.querySelector('.fixed.top-4.right-4');
  if (!floatingPanel) {
    console.log('ℹ️ 当前没有悬浮控制面板');
    return;
  }
  
  console.log('找到悬浮控制面板，模拟外部点击...');
  
  // 模拟点击矩阵区域
  const matrixArea = document.querySelector('.matrix-area');
  if (matrixArea) {
    const clickEvent = new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    
    matrixArea.dispatchEvent(clickEvent);
    
    setTimeout(() => {
      const panelStillVisible = document.querySelector('.fixed.top-4.right-4');
      
      if (!panelStillVisible) {
        console.log('✅ 点击外部自动隐藏工作正常');
      } else {
        console.log('❌ 点击外部未能隐藏控制面板');
      }
    }, TEST_CONFIG.ANIMATION_DELAY);
  } else {
    console.log('❌ 未找到矩阵区域');
  }
}

// 导出测试函数到全局作用域
if (typeof window !== 'undefined') {
  window.testControlsPanelFixes = testControlsPanelFixes;
  window.manualTestHideButton = manualTestHideButton;
  window.manualTestClickOutside = manualTestClickOutside;
}

// 自动运行测试（如果在浏览器环境中）
if (typeof window !== 'undefined' && document.readyState === 'complete') {
  console.log('🚀 自动运行控制面板修复测试...');
  testControlsPanelFixes();
} else if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    console.log('🚀 页面加载完成，运行控制面板修复测试...');
    testControlsPanelFixes();
  });
}

console.log('📝 控制面板修复测试脚本已加载');
console.log('💡 可用的手动测试函数:');
console.log('   - testControlsPanelFixes() - 运行完整测试');
console.log('   - manualTestHideButton() - 测试隐藏按钮');
console.log('   - manualTestClickOutside() - 测试点击外部');
